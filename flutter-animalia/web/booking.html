<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Book Appointment</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .header {
      text-align: center;
      margin-bottom: 30px;
    }
    .loading {
      text-align: center;
      padding: 50px;
    }
    .error {
      color: #d32f2f;
      text-align: center;
      padding: 20px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Book Your Appointment</h1>
      <p>Schedule your pet grooming appointment online</p>
    </div>
    
    <div id="loading" class="loading">
      <p>Loading booking form...</p>
    </div>
    
    <div id="error" class="error" style="display: none;">
      <p>Unable to load booking form. Please try again later.</p>
    </div>
    
    <div id="flutter-app" style="display: none;"></div>
  </div>

  <script>
    // Extract salon ID from URL
    function getSalonIdFromUrl() {
      const path = window.location.pathname;
      const segments = path.split('/');
      // Expected format: /booking/{salonId} or /public/booking/{salonId}
      const bookingIndex = segments.indexOf('booking');
      if (bookingIndex !== -1 && bookingIndex + 1 < segments.length) {
        return segments[bookingIndex + 1];
      }
      return null;
    }

    // Initialize the booking app
    function initializeBookingApp() {
      const salonId = getSalonIdFromUrl();
      
      if (!salonId) {
        document.getElementById('loading').style.display = 'none';
        document.getElementById('error').style.display = 'block';
        document.getElementById('error').innerHTML = '<p>Invalid booking URL. Salon ID not found.</p>';
        return;
      }

      // Store salon ID for Flutter app to access
      window.bookingSalonId = salonId;
      
      // Show Flutter app container
      document.getElementById('loading').style.display = 'none';
      document.getElementById('flutter-app').style.display = 'block';
      
      // Load Flutter app (this would be replaced with actual Flutter web build)
      loadFlutterApp();
    }

    function loadFlutterApp() {
      // This is a placeholder - in a real implementation, this would load the Flutter web app
      // For now, we'll show a simple form
      const flutterContainer = document.getElementById('flutter-app');
      flutterContainer.innerHTML = `
        <div style="text-align: center; padding: 50px;">
          <h2>Booking Form</h2>
          <p>Salon ID: ${window.bookingSalonId}</p>
          <p>Flutter booking app would load here</p>
          <p>To test the backend API, open browser console and check network requests to:</p>
          <ul style="text-align: left; display: inline-block;">
            <li>GET /public/booking/salon/${window.bookingSalonId}</li>
            <li>GET /public/booking/salon/${window.bookingSalonId}/services</li>
            <li>GET /public/booking/salon/${window.bookingSalonId}/staff</li>
          </ul>
        </div>
      `;
    }

    // Initialize when page loads
    document.addEventListener('DOMContentLoaded', initializeBookingApp);
  </script>
</body>
</html>
