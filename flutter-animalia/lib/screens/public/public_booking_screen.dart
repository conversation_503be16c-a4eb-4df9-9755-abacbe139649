import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../models/salon_web_preferences.dart';
import '../../services/public_booking_service.dart';
import '../../widgets/common/loading_overlay.dart';
import '../../widgets/common/custom_button.dart';
import '../../widgets/common/custom_text_field.dart';
import '../../l10n/app_localizations.dart';

/// Public booking screen for scheduling appointments without authentication
class PublicBookingScreen extends StatefulWidget {
  final String salonId;
  final String? bookingUrl;

  const PublicBookingScreen({
    super.key,
    required this.salonId,
    this.bookingUrl,
  });

  @override
  State<PublicBookingScreen> createState() => _PublicBookingScreenState();
}

class _PublicBookingScreenState extends State<PublicBookingScreen> {
  final _formKey = GlobalKey<FormState>();
  final _publicBookingService = PublicBookingService();
  
  // Form controllers
  final _clientNameController = TextEditingController();
  final _clientPhoneController = TextEditingController();
  final _clientEmailController = TextEditingController();
  final _petNameController = TextEditingController();
  final _petBreedController = TextEditingController();
  final _notesController = TextEditingController();
  
  // State variables
  bool _isLoading = true;
  bool _isSubmitting = false;
  
  // Data
  PublicSalonInfo? _salonInfo;
  List<PublicService> _services = [];
  List<PublicStaff> _staff = [];
  List<TimeSlot> _availableSlots = [];
  
  // Selected values
  String? _selectedStaffId;
  List<String> _selectedServiceIds = [];
  String _selectedPetSpecies = 'Dog';
  String? _selectedPetSize;
  DateTime? _selectedDate;
  TimeSlot? _selectedTimeSlot;
  
  final List<String> _petSpeciesOptions = ['Dog', 'Cat', 'Other'];
  final List<String> _petSizeOptions = ['Small', 'Medium', 'Large', 'Extra Large'];

  @override
  void initState() {
    super.initState();
    _loadSalonData();
  }

  @override
  void dispose() {
    _clientNameController.dispose();
    _clientPhoneController.dispose();
    _clientEmailController.dispose();
    _petNameController.dispose();
    _petBreedController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _loadSalonData() async {
    try {
      setState(() => _isLoading = true);
      
      // Load salon info, services, and staff in parallel
      final results = await Future.wait([
        _publicBookingService.getSalonInfo(widget.salonId),
        _publicBookingService.getSalonServices(widget.salonId),
        _publicBookingService.getSalonStaff(widget.salonId),
      ]);
      
      setState(() {
        _salonInfo = results[0] as PublicSalonInfo?;
        _services = results[1] as List<PublicService>;
        _staff = results[2] as List<PublicStaff>;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to load salon data: $e')),
        );
      }
    }
  }

  Future<void> _checkAvailability() async {
    if (_selectedStaffId == null || _selectedServiceIds.isEmpty || _selectedDate == null) {
      return;
    }

    try {
      final availability = await _publicBookingService.checkAvailability(
        widget.salonId,
        _selectedStaffId!,
        _selectedDate!,
        _selectedServiceIds,
      );
      
      setState(() {
        _availableSlots = availability.availableSlots;
        _selectedTimeSlot = null; // Reset selected time slot
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to check availability: $e')),
        );
      }
    }
  }

  Future<void> _submitBooking() async {
    if (!_formKey.currentState!.validate() || 
        _selectedStaffId == null || 
        _selectedServiceIds.isEmpty || 
        _selectedDate == null || 
        _selectedTimeSlot == null) {
      return;
    }

    try {
      setState(() => _isSubmitting = true);
      
      final booking = await _publicBookingService.createBooking(
        salonId: widget.salonId,
        clientName: _clientNameController.text.trim(),
        clientPhone: _clientPhoneController.text.trim(),
        clientEmail: _clientEmailController.text.trim().isEmpty ? null : _clientEmailController.text.trim(),
        petName: _petNameController.text.trim(),
        petSpecies: _selectedPetSpecies,
        petBreed: _petBreedController.text.trim().isEmpty ? null : _petBreedController.text.trim(),
        petSize: _selectedPetSize,
        staffId: _selectedStaffId!,
        appointmentDate: _selectedDate!,
        startTime: _selectedTimeSlot!.startTime,
        endTime: _selectedTimeSlot!.endTime,
        serviceIds: _selectedServiceIds,
        notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
      );
      
      if (mounted) {
        _showBookingConfirmation(booking);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to create booking: $e')),
        );
      }
    } finally {
      setState(() => _isSubmitting = false);
    }
  }

  void _showBookingConfirmation(PublicBookingResponse booking) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Booking Confirmed'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(booking.confirmationMessage),
            const SizedBox(height: 16),
            Text('Appointment ID: ${booking.appointmentId}'),
            Text('Date: ${DateFormat('yyyy-MM-dd').format(booking.appointmentDate)}'),
            Text('Time: ${booking.startTime.format(context)} - ${booking.endTime.format(context)}'),
            Text('Staff: ${booking.staffName}'),
            Text('Total: ${booking.totalPrice} RON'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop(); // Go back to previous screen
            },
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    if (_salonInfo == null) {
      return Scaffold(
        appBar: AppBar(title: const Text('Booking')),
        body: const Center(
          child: Text('Salon not found or not available for booking'),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text('Book at ${_salonInfo!.businessName}'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: LoadingOverlay(
        isLoading: _isSubmitting,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildSalonInfoSection(),
                const SizedBox(height: 24),
                _buildClientInfoSection(),
                const SizedBox(height: 24),
                _buildPetInfoSection(),
                const SizedBox(height: 24),
                _buildServiceSelectionSection(),
                const SizedBox(height: 24),
                _buildStaffSelectionSection(),
                const SizedBox(height: 24),
                _buildDateTimeSelectionSection(),
                const SizedBox(height: 24),
                _buildNotesSection(),
                const SizedBox(height: 32),
                _buildSubmitButton(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSalonInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              _salonInfo!.businessName,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            if (_salonInfo!.businessDescription.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(_salonInfo!.businessDescription),
            ],
            if (_salonInfo!.businessAddress.isNotEmpty) ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  const Icon(Icons.location_on, size: 16),
                  const SizedBox(width: 4),
                  Expanded(child: Text(_salonInfo!.businessAddress)),
                ],
              ),
            ],
            if (_salonInfo!.contactPhone.isNotEmpty) ...[
              const SizedBox(height: 4),
              Row(
                children: [
                  const Icon(Icons.phone, size: 16),
                  const SizedBox(width: 4),
                  Text(_salonInfo!.contactPhone),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildClientInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Your Information',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            CustomTextField(
              controller: _clientNameController,
              labelText: 'Full Name *',
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Name is required';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            CustomTextField(
              controller: _clientPhoneController,
              labelText: 'Phone Number *',
              keyboardType: TextInputType.phone,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Phone number is required';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            CustomTextField(
              controller: _clientEmailController,
              labelText: 'Email (Optional)',
              keyboardType: TextInputType.emailAddress,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPetInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Pet Information',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            CustomTextField(
              controller: _petNameController,
              labelText: 'Pet Name *',
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Pet name is required';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: _selectedPetSpecies,
              decoration: const InputDecoration(
                labelText: 'Species *',
                border: OutlineInputBorder(),
              ),
              items: _petSpeciesOptions.map((species) {
                return DropdownMenuItem(
                  value: species,
                  child: Text(species),
                );
              }).toList(),
              onChanged: (value) {
                setState(() => _selectedPetSpecies = value!);
              },
            ),
            const SizedBox(height: 16),
            CustomTextField(
              controller: _petBreedController,
              labelText: 'Breed (Optional)',
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: _selectedPetSize,
              decoration: const InputDecoration(
                labelText: 'Size (Optional)',
                border: OutlineInputBorder(),
              ),
              items: _petSizeOptions.map((size) {
                return DropdownMenuItem(
                  value: size,
                  child: Text(size),
                );
              }).toList(),
              onChanged: (value) {
                setState(() => _selectedPetSize = value);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildServiceSelectionSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Select Services *',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            if (_services.isEmpty)
              const Text('No services available')
            else
              ..._services.map((service) {
                final isSelected = _selectedServiceIds.contains(service.id);
                return CheckboxListTile(
                  title: Text(service.name),
                  subtitle: Text('${service.formattedPrice} • ${service.formattedDuration}'),
                  value: isSelected,
                  onChanged: (selected) {
                    setState(() {
                      if (selected == true) {
                        _selectedServiceIds.add(service.id);
                      } else {
                        _selectedServiceIds.remove(service.id);
                      }
                    });
                    _checkAvailability();
                  },
                );
              }).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildStaffSelectionSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Select Staff *',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            if (_staff.isEmpty)
              const Text('No staff available')
            else
              ..._staff.map((staffMember) {
                return RadioListTile<String>(
                  title: Text(staffMember.name),
                  subtitle: staffMember.specialties.isNotEmpty
                      ? Text(staffMember.specialties.join(', '))
                      : null,
                  value: staffMember.id,
                  groupValue: _selectedStaffId,
                  onChanged: (value) {
                    setState(() => _selectedStaffId = value);
                    _checkAvailability();
                  },
                );
              }).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildDateTimeSelectionSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Select Date & Time *',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ListTile(
              title: Text(_selectedDate == null
                  ? 'Select Date'
                  : DateFormat('yyyy-MM-dd').format(_selectedDate!)),
              trailing: const Icon(Icons.calendar_today),
              onTap: () async {
                final date = await showDatePicker(
                  context: context,
                  initialDate: DateTime.now().add(const Duration(days: 1)),
                  firstDate: DateTime.now(),
                  lastDate: DateTime.now().add(const Duration(days: 90)),
                );
                if (date != null) {
                  setState(() => _selectedDate = date);
                  _checkAvailability();
                }
              },
            ),
            const SizedBox(height: 16),
            if (_availableSlots.isNotEmpty) ...[
              Text(
                'Available Times:',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: _availableSlots.map((slot) {
                  final isSelected = _selectedTimeSlot == slot;
                  return FilterChip(
                    label: Text('${slot.startTime.format(context)} - ${slot.endTime.format(context)}'),
                    selected: isSelected,
                    onSelected: slot.available ? (selected) {
                      setState(() => _selectedTimeSlot = selected ? slot : null);
                    } : null,
                  );
                }).toList(),
              ),
            ] else if (_selectedDate != null && _selectedStaffId != null && _selectedServiceIds.isNotEmpty)
              const Text('No available time slots for the selected date.'),
          ],
        ),
      ),
    );
  }

  Widget _buildNotesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Additional Notes (Optional)',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            CustomTextField(
              controller: _notesController,
              labelText: 'Any special requests or notes...',
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubmitButton() {
    final canSubmit = _selectedStaffId != null &&
                     _selectedServiceIds.isNotEmpty &&
                     _selectedDate != null &&
                     _selectedTimeSlot != null;

    return SizedBox(
      width: double.infinity,
      child: CustomButton(
        text: 'Book Appointment',
        onPressed: canSubmit ? _submitBooking : null,
        isLoading: _isSubmitting,
      ),
    );
  }
}
