import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import '../config/api_config.dart';
import '../utils/debug_logger.dart';

/// Service for public booking functionality (no authentication required)
class PublicBookingService {
  static const String _baseUrl = '${ApiConfig.baseUrl}/public/booking';

  /// Get public salon information
  Future<PublicSalonInfo?> getSalonInfo(String salonId) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/salon/$salonId'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true && data['data'] != null) {
          return PublicSalonInfo.fromJson(data['data']);
        }
      } else if (response.statusCode == 404) {
        return null;
      }

      throw Exception('Failed to get salon info: ${response.statusCode}');
    } catch (e) {
      DebugLogger.logError('Error getting salon info: $e');
      rethrow;
    }
  }

  /// Get salon services
  Future<List<PublicService>> getSalonServices(String salonId) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/salon/$salonId/services'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true && data['data'] != null) {
          return (data['data'] as List)
              .map((json) => PublicService.fromJson(json))
              .toList();
        }
      }

      throw Exception('Failed to get salon services: ${response.statusCode}');
    } catch (e) {
      DebugLogger.logError('Error getting salon services: $e');
      rethrow;
    }
  }

  /// Get salon staff
  Future<List<PublicStaff>> getSalonStaff(String salonId) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/salon/$salonId/staff'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true && data['data'] != null) {
          return (data['data'] as List)
              .map((json) => PublicStaff.fromJson(json))
              .toList();
        }
      }

      throw Exception('Failed to get salon staff: ${response.statusCode}');
    } catch (e) {
      DebugLogger.logError('Error getting salon staff: $e');
      rethrow;
    }
  }

  /// Check availability for appointment booking
  Future<AvailabilityResponse> checkAvailability(
    String salonId,
    String staffId,
    DateTime date,
    List<String> serviceIds,
  ) async {
    try {
      final requestBody = {
        'staffId': staffId,
        'appointmentDate': date.toIso8601String().split('T')[0],
        'serviceIds': serviceIds,
      };

      final response = await http.post(
        Uri.parse('$_baseUrl/salon/$salonId/check-availability'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode(requestBody),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true && data['data'] != null) {
          return AvailabilityResponse.fromJson(data['data']);
        }
      }

      throw Exception('Failed to check availability: ${response.statusCode}');
    } catch (e) {
      DebugLogger.logError('Error checking availability: $e');
      rethrow;
    }
  }

  /// Create a public booking
  Future<PublicBookingResponse> createBooking({
    required String salonId,
    required String clientName,
    required String clientPhone,
    String? clientEmail,
    required String petName,
    required String petSpecies,
    String? petBreed,
    String? petSize,
    required String staffId,
    required DateTime appointmentDate,
    required TimeOfDay startTime,
    required TimeOfDay endTime,
    required List<String> serviceIds,
    String? notes,
  }) async {
    try {
      final requestBody = {
        'clientName': clientName,
        'clientPhone': clientPhone,
        if (clientEmail != null) 'clientEmail': clientEmail,
        'petName': petName,
        'petSpecies': petSpecies,
        if (petBreed != null) 'petBreed': petBreed,
        if (petSize != null) 'petSize': petSize,
        'staffId': staffId,
        'appointmentDate': appointmentDate.toIso8601String().split('T')[0],
        'startTime': '${startTime.hour.toString().padLeft(2, '0')}:${startTime.minute.toString().padLeft(2, '0')}',
        'endTime': '${endTime.hour.toString().padLeft(2, '0')}:${endTime.minute.toString().padLeft(2, '0')}',
        'serviceIds': serviceIds,
        if (notes != null) 'notes': notes,
      };

      final response = await http.post(
        Uri.parse('$_baseUrl/salon/$salonId/book'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode(requestBody),
      );

      if (response.statusCode == 201) {
        final data = json.decode(response.body);
        if (data['success'] == true && data['data'] != null) {
          return PublicBookingResponse.fromJson(data['data']);
        }
      }

      throw Exception('Failed to create booking: ${response.statusCode}');
    } catch (e) {
      DebugLogger.logError('Error creating booking: $e');
      rethrow;
    }
  }
}

/// Model classes for public booking

class PublicSalonInfo {
  final String salonId;
  final String businessName;
  final String businessDescription;
  final String businessAddress;
  final String contactPhone;
  final String contactEmail;
  final String? facebookLink;
  final String? instagramLink;
  final String? tiktokLink;
  final List<String> websitePhotos;
  final Map<String, dynamic> businessHours;
  final String cancellationPolicy;
  final String bookingAcceptance;

  PublicSalonInfo({
    required this.salonId,
    required this.businessName,
    required this.businessDescription,
    required this.businessAddress,
    required this.contactPhone,
    required this.contactEmail,
    this.facebookLink,
    this.instagramLink,
    this.tiktokLink,
    required this.websitePhotos,
    required this.businessHours,
    required this.cancellationPolicy,
    required this.bookingAcceptance,
  });

  factory PublicSalonInfo.fromJson(Map<String, dynamic> json) {
    return PublicSalonInfo(
      salonId: json['salonId'] ?? '',
      businessName: json['businessName'] ?? '',
      businessDescription: json['businessDescription'] ?? '',
      businessAddress: json['businessAddress'] ?? '',
      contactPhone: json['contactPhone'] ?? '',
      contactEmail: json['contactEmail'] ?? '',
      facebookLink: json['facebookLink'],
      instagramLink: json['instagramLink'],
      tiktokLink: json['tiktokLink'],
      websitePhotos: List<String>.from(json['websitePhotos'] ?? []),
      businessHours: Map<String, dynamic>.from(json['businessHours'] ?? {}),
      cancellationPolicy: json['cancellationPolicy'] ?? '',
      bookingAcceptance: json['bookingAcceptance'] ?? '',
    );
  }
}

class PublicService {
  final String id;
  final String name;
  final String? description;
  final double price;
  final int duration;
  final String category;
  final String formattedPrice;
  final String formattedDuration;

  PublicService({
    required this.id,
    required this.name,
    this.description,
    required this.price,
    required this.duration,
    required this.category,
    required this.formattedPrice,
    required this.formattedDuration,
  });

  factory PublicService.fromJson(Map<String, dynamic> json) {
    return PublicService(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'],
      price: (json['price'] ?? 0).toDouble(),
      duration: json['duration'] ?? 0,
      category: json['category'] ?? '',
      formattedPrice: json['formattedPrice'] ?? '',
      formattedDuration: json['formattedDuration'] ?? '',
    );
  }
}

class PublicStaff {
  final String id;
  final String name;
  final List<String> specialties;

  PublicStaff({
    required this.id,
    required this.name,
    required this.specialties,
  });

  factory PublicStaff.fromJson(Map<String, dynamic> json) {
    return PublicStaff(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      specialties: List<String>.from(json['specialties'] ?? []),
    );
  }
}

class TimeSlot {
  final TimeOfDay startTime;
  final TimeOfDay endTime;
  final bool available;

  TimeSlot({
    required this.startTime,
    required this.endTime,
    required this.available,
  });

  factory TimeSlot.fromJson(Map<String, dynamic> json) {
    return TimeSlot(
      startTime: _parseTimeOfDay(json['startTime']),
      endTime: _parseTimeOfDay(json['endTime']),
      available: json['available'] ?? false,
    );
  }

  static TimeOfDay _parseTimeOfDay(String timeString) {
    final parts = timeString.split(':');
    return TimeOfDay(
      hour: int.parse(parts[0]),
      minute: int.parse(parts[1]),
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TimeSlot &&
          runtimeType == other.runtimeType &&
          startTime == other.startTime &&
          endTime == other.endTime;

  @override
  int get hashCode => startTime.hashCode ^ endTime.hashCode;
}

class AvailabilityResponse {
  final DateTime date;
  final List<TimeSlot> availableSlots;

  AvailabilityResponse({
    required this.date,
    required this.availableSlots,
  });

  factory AvailabilityResponse.fromJson(Map<String, dynamic> json) {
    return AvailabilityResponse(
      date: DateTime.parse(json['date']),
      availableSlots: (json['availableSlots'] as List)
          .map((slot) => TimeSlot.fromJson(slot))
          .toList(),
    );
  }
}

class PublicBookingResponse {
  final String appointmentId;
  final String salonName;
  final String clientName;
  final String petName;
  final String staffName;
  final DateTime appointmentDate;
  final TimeOfDay startTime;
  final TimeOfDay endTime;
  final List<PublicService> services;
  final double totalPrice;
  final String status;
  final String? notes;
  final String bookingAcceptance;
  final String confirmationMessage;

  PublicBookingResponse({
    required this.appointmentId,
    required this.salonName,
    required this.clientName,
    required this.petName,
    required this.staffName,
    required this.appointmentDate,
    required this.startTime,
    required this.endTime,
    required this.services,
    required this.totalPrice,
    required this.status,
    this.notes,
    required this.bookingAcceptance,
    required this.confirmationMessage,
  });

  factory PublicBookingResponse.fromJson(Map<String, dynamic> json) {
    return PublicBookingResponse(
      appointmentId: json['appointmentId'] ?? '',
      salonName: json['salonName'] ?? '',
      clientName: json['clientName'] ?? '',
      petName: json['petName'] ?? '',
      staffName: json['staffName'] ?? '',
      appointmentDate: DateTime.parse(json['appointmentDate']),
      startTime: TimeSlot._parseTimeOfDay(json['startTime']),
      endTime: TimeSlot._parseTimeOfDay(json['endTime']),
      services: (json['services'] as List)
          .map((service) => PublicService.fromJson(service))
          .toList(),
      totalPrice: (json['totalPrice'] ?? 0).toDouble(),
      status: json['status'] ?? '',
      notes: json['notes'],
      bookingAcceptance: json['bookingAcceptance'] ?? '',
      confirmationMessage: json['confirmationMessage'] ?? '',
    );
  }
}
