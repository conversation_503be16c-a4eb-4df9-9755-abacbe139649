package ro.animaliaprogramari.animalia.adapter.inbound.rest

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.*
import ro.animaliaprogramari.animalia.application.port.inbound.AppointmentManagementUseCase
import ro.animaliaprogramari.animalia.application.port.inbound.ClientManagementUseCase
import ro.animaliaprogramari.animalia.application.port.inbound.PetManagementUseCase
import ro.animaliaprogramari.animalia.application.port.inbound.SalonManagementUseCase
import ro.animaliaprogramari.animalia.application.service.SalonWebPreferencesService
import ro.animaliaprogramari.animalia.application.service.ServiceManagementService
import ro.animaliaprogramari.animalia.application.service.StaffManagementService
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.domain.service.AppointmentSchedulingService
import java.time.LocalDate
import java.time.LocalTime

/**
 * REST controller for public booking functionality
 * Allows users to book appointments without authentication
 */
@RestController
@RequestMapping("/public/booking")
@Tag(name = "Public Booking", description = "Public booking operations for salon appointments")
class PublicBookingController(
    private val salonWebPreferencesService: SalonWebPreferencesService,
    private val serviceManagementService: ServiceManagementService,
    private val staffManagementService: StaffManagementService,
    private val appointmentManagementUseCase: AppointmentManagementUseCase,
    private val clientManagementUseCase: ClientManagementUseCase,
    private val petManagementUseCase: PetManagementUseCase,
    private val salonManagementUseCase: SalonManagementUseCase,
    private val appointmentSchedulingService: AppointmentSchedulingService,
) {
    private val logger = LoggerFactory.getLogger(PublicBookingController::class.java)

    /**
     * Get public salon information for booking
     */
    @GetMapping("/salon/{salonId}")
    @Operation(summary = "Get public salon information", description = "Retrieve salon information for public booking")
    fun getSalonInfo(
        @Parameter(description = "Salon ID") @PathVariable salonId: String
    ): ResponseEntity<ApiResponse<PublicSalonInfoResponse>> {
        return try {
            logger.info("Getting public salon info for salon: $salonId")
            
            val webPreferences = salonWebPreferencesService.getWebPreferences(SalonId.of(salonId))
            
            if (webPreferences == null) {
                return ResponseEntity.notFound().build()
            }
            
            val response = PublicSalonInfoResponse(
                salonId = salonId,
                businessName = webPreferences.businessName,
                businessDescription = webPreferences.businessDescription,
                businessAddress = webPreferences.businessAddress,
                contactPhone = webPreferences.contactPhone,
                contactEmail = webPreferences.contactEmail,
                facebookLink = webPreferences.facebookLink.takeIf { it.isNotBlank() },
                instagramLink = webPreferences.instagramLink.takeIf { it.isNotBlank() },
                tiktokLink = webPreferences.tiktokLink.takeIf { it.isNotBlank() },
                websitePhotos = webPreferences.websitePhotos,
                businessHours = webPreferences.businessHours,
                cancellationPolicy = webPreferences.cancellationPolicy.name,
                bookingAcceptance = webPreferences.bookingAcceptance.name,
            )
            
            ResponseEntity.ok(ApiResponse.success(response))
        } catch (e: Exception) {
            logger.error("Failed to get salon info for salon: $salonId", e)
            ResponseEntity.internalServerError().body(
                ApiResponse.error("Failed to get salon information: ${e.message}")
            )
        }
    }

    /**
     * Get available services for a salon
     */
    @GetMapping("/salon/{salonId}/services")
    @Operation(summary = "Get salon services", description = "Retrieve available services for public booking")
    fun getSalonServices(
        @Parameter(description = "Salon ID") @PathVariable salonId: String
    ): ResponseEntity<ApiResponse<List<PublicServiceResponse>>> {
        return try {
            logger.info("Getting services for salon: $salonId")
            
            val services = serviceManagementService.getActiveServices(SalonId.of(salonId))
            
            val response = services.map { service ->
                PublicServiceResponse(
                    id = service.id.value,
                    name = service.name,
                    description = service.description,
                    price = service.basePrice.amount,
                    duration = service.myDuration.minutes,
                    category = service.category.name,
                    formattedPrice = "${service.basePrice.amount} ${service.basePrice.currency}",
                    formattedDuration = "${service.myDuration.minutes} min",
                    sizePrices = service.sizePrices.mapValues { it.value.amount },
                    sizeDurations = service.sizeDurations.mapValues { it.value.minutes },
                    minPrice = service.minPrice?.amount,
                    maxPrice = service.maxPrice?.amount,
                )
            }
            
            ResponseEntity.ok(ApiResponse.success(response))
        } catch (e: Exception) {
            logger.error("Failed to get services for salon: $salonId", e)
            ResponseEntity.internalServerError().body(
                ApiResponse.error("Failed to get salon services: ${e.message}")
            )
        }
    }

    /**
     * Get available staff for a salon
     */
    @GetMapping("/salon/{salonId}/staff")
    @Operation(summary = "Get salon staff", description = "Retrieve available staff for public booking")
    fun getSalonStaff(
        @Parameter(description = "Salon ID") @PathVariable salonId: String
    ): ResponseEntity<ApiResponse<List<PublicStaffResponse>>> {
        return try {
            logger.info("Getting staff for salon: $salonId")
            
            val staff = staffManagementService.getActiveStaff(SalonId.of(salonId))
            
            val response = staff.map { staffMember ->
                PublicStaffResponse(
                    id = staffMember.id.value,
                    name = staffMember.name,
                    specialties = staffMember.specialties,
                )
            }
            
            ResponseEntity.ok(ApiResponse.success(response))
        } catch (e: Exception) {
            logger.error("Failed to get staff for salon: $salonId", e)
            ResponseEntity.internalServerError().body(
                ApiResponse.error("Failed to get salon staff: ${e.message}")
            )
        }
    }

    /**
     * Check availability for appointment booking
     */
    @PostMapping("/salon/{salonId}/check-availability")
    @Operation(summary = "Check availability", description = "Check available time slots for booking")
    fun checkAvailability(
        @Parameter(description = "Salon ID") @PathVariable salonId: String,
        @Valid @RequestBody request: CheckAvailabilityRequest
    ): ResponseEntity<ApiResponse<AvailabilityResponse>> {
        return try {
            logger.info("Checking availability for salon: $salonId, date: ${request.appointmentDate}")
            
            // Get services to calculate total duration
            val services = serviceManagementService.getServicesByIds(
                SalonId.of(salonId),
                request.serviceIds.map { ServiceId.of(it) }
            )
            
            val totalDuration = services.sumOf { it.myDuration.minutes }
            
            // Generate time slots for the day (example: 9 AM to 6 PM, 30-minute intervals)
            val timeSlots = generateTimeSlots(
                startTime = LocalTime.of(9, 0),
                endTime = LocalTime.of(18, 0),
                intervalMinutes = 30,
                serviceDurationMinutes = totalDuration
            )
            
            // TODO: Check actual availability against existing appointments
            // For now, mark all slots as available
            val availableSlots = timeSlots.map { slot ->
                TimeSlot(
                    startTime = slot.first,
                    endTime = slot.second,
                    available = true
                )
            }
            
            val response = AvailabilityResponse(
                date = request.appointmentDate,
                availableSlots = availableSlots
            )
            
            ResponseEntity.ok(ApiResponse.success(response))
        } catch (e: Exception) {
            logger.error("Failed to check availability for salon: $salonId", e)
            ResponseEntity.internalServerError().body(
                ApiResponse.error("Failed to check availability: ${e.message}")
            )
        }
    }

    /**
     * Generate time slots for a given time range
     */
    private fun generateTimeSlots(
        startTime: LocalTime,
        endTime: LocalTime,
        intervalMinutes: Int,
        serviceDurationMinutes: Int
    ): List<Pair<LocalTime, LocalTime>> {
        val slots = mutableListOf<Pair<LocalTime, LocalTime>>()
        var currentTime = startTime
        
        while (currentTime.plusMinutes(serviceDurationMinutes.toLong()).isBefore(endTime) ||
               currentTime.plusMinutes(serviceDurationMinutes.toLong()).equals(endTime)) {
            val slotEndTime = currentTime.plusMinutes(serviceDurationMinutes.toLong())
            slots.add(Pair(currentTime, slotEndTime))
            currentTime = currentTime.plusMinutes(intervalMinutes.toLong())
        }
        
        return slots
    }

    /**
     * Create a public booking
     */
    @PostMapping("/salon/{salonId}/book")
    @Operation(summary = "Create public booking", description = "Create an appointment booking without authentication")
    fun createBooking(
        @Parameter(description = "Salon ID") @PathVariable salonId: String,
        @Valid @RequestBody request: CreatePublicBookingRequest
    ): ResponseEntity<ApiResponse<PublicBookingResponse>> {
        return try {
            logger.info("Creating public booking for salon: $salonId")

            // Get or create client
            val clientPhone = PhoneNumber.of(request.clientPhone)
            val existingClient = clientManagementUseCase.findClientByPhone(SalonId.of(salonId), clientPhone)

            val client = existingClient ?: run {
                // Create new client
                val createClientCommand = ro.animaliaprogramari.animalia.application.command.CreateClientCommand(
                    salonId = SalonId.of(salonId),
                    name = request.clientName,
                    phone = clientPhone,
                    email = request.clientEmail?.let { Email.of(it) },
                    address = null,
                    notes = "Created via public booking"
                )
                clientManagementUseCase.createClient(createClientCommand)
            }

            // Get or create pet
            val existingPet = petManagementUseCase.findPetByNameAndClient(client.id, request.petName)

            val pet = existingPet ?: run {
                // Create new pet
                val createPetCommand = ro.animaliaprogramari.animalia.application.command.CreatePetCommand(
                    clientId = client.id,
                    name = request.petName,
                    species = request.petSpecies,
                    breed = request.petBreed,
                    size = request.petSize,
                    notes = "Created via public booking"
                )
                petManagementUseCase.createPet(createPetCommand)
            }

            // Get services
            val services = serviceManagementService.getServicesByIds(
                SalonId.of(salonId),
                request.serviceIds.map { ServiceId.of(it) }
            )

            // Create appointment
            val scheduleCommand = ro.animaliaprogramari.animalia.application.command.ScheduleAppointmentCommand(
                salonId = SalonId.of(salonId),
                clientId = client.id,
                clientName = client.name,
                clientPhone = client.phone,
                petId = pet.id,
                petName = pet.name,
                petSpecies = pet.species,
                petBreed = pet.breed,
                petSize = pet.size,
                staffId = StaffId.of(request.staffId),
                appointmentDate = request.appointmentDate,
                startTime = request.startTime,
                endTime = request.endTime,
                serviceIds = request.serviceIds.map { ServiceId.of(it) },
                notes = request.notes,
                isNewClient = existingClient == null,
                isNewPet = existingPet == null
            )

            val appointment = appointmentManagementUseCase.scheduleAppointment(scheduleCommand)

            // Get salon and staff info for response
            val salon = salonManagementUseCase.getSalonById(SalonId.of(salonId))
            val staff = staffManagementService.getStaffById(StaffId.of(request.staffId))
            val webPreferences = salonWebPreferencesService.getWebPreferences(SalonId.of(salonId))

            val response = PublicBookingResponse(
                appointmentId = appointment.id.value,
                salonName = salon?.name ?: "Unknown Salon",
                clientName = client.name,
                petName = pet.name,
                staffName = staff?.name ?: "Unknown Staff",
                appointmentDate = appointment.appointmentDate,
                startTime = appointment.startTime,
                endTime = appointment.endTime,
                services = services.map { service ->
                    PublicServiceResponse(
                        id = service.id.value,
                        name = service.name,
                        description = service.description,
                        price = service.basePrice.amount,
                        duration = service.myDuration.minutes,
                        category = service.category.name,
                        formattedPrice = "${service.basePrice.amount} ${service.basePrice.currency}",
                        formattedDuration = "${service.myDuration.minutes} min",
                        sizePrices = service.sizePrices.mapValues { it.value.amount },
                        sizeDurations = service.sizeDurations.mapValues { it.value.minutes },
                        minPrice = service.minPrice?.amount,
                        maxPrice = service.maxPrice?.amount,
                    )
                },
                totalPrice = appointment.totalPrice.amount,
                status = appointment.status.name,
                notes = appointment.notes,
                bookingAcceptance = webPreferences?.bookingAcceptance?.name ?: "MANUAL",
                confirmationMessage = if (webPreferences?.bookingAcceptance == BookingAcceptance.AUTOMATIC) {
                    "Your appointment has been confirmed automatically."
                } else {
                    "Your appointment request has been submitted and is pending approval."
                }
            )

            ResponseEntity.status(HttpStatus.CREATED).body(ApiResponse.success(response))
        } catch (e: Exception) {
            logger.error("Failed to create booking for salon: $salonId", e)
            ResponseEntity.internalServerError().body(
                ApiResponse.error("Failed to create booking: ${e.message}")
            )
        }
    }
}
