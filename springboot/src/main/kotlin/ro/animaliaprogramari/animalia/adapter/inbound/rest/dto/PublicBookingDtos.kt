package ro.animaliaprogramari.animalia.adapter.inbound.rest.dto

import com.fasterxml.jackson.annotation.JsonFormat
import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.constraints.*
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalTime

/**
 * Response DTO for public salon information
 */
@Schema(description = "Public salon information for booking")
data class PublicSalonInfoResponse(
    @JsonProperty("salonId")
    val salonId: String,
    @JsonProperty("businessName")
    val businessName: String,
    @JsonProperty("businessDescription")
    val businessDescription: String,
    @JsonProperty("businessAddress")
    val businessAddress: String,
    @JsonProperty("contactPhone")
    val contactPhone: String,
    @JsonProperty("contactEmail")
    val contactEmail: String,
    @JsonProperty("facebookLink")
    val facebookLink: String?,
    @JsonProperty("instagramLink")
    val instagramLink: String?,
    @JsonProperty("tiktokLink")
    val tiktokLink: String?,
    @JsonProperty("websitePhotos")
    val websitePhotos: List<String>,
    @JsonProperty("businessHours")
    val businessHours: Map<String, Map<String, Any>>,
    @JsonProperty("cancellationPolicy")
    val cancellationPolicy: String,
    @JsonProperty("bookingAcceptance")
    val bookingAcceptance: String,
)

/**
 * Response DTO for public service information
 */
@Schema(description = "Public service information for booking")
data class PublicServiceResponse(
    @JsonProperty("id")
    val id: String,
    @JsonProperty("name")
    val name: String,
    @JsonProperty("description")
    val description: String?,
    @JsonProperty("price")
    val price: BigDecimal,
    @JsonProperty("duration")
    val duration: Int, // in minutes
    @JsonProperty("category")
    val category: String,
    @JsonProperty("formattedPrice")
    val formattedPrice: String,
    @JsonProperty("formattedDuration")
    val formattedDuration: String,
    // Variable pricing fields
    @JsonProperty("sizePrices")
    val sizePrices: Map<String, BigDecimal>?,
    @JsonProperty("sizeDurations")
    val sizeDurations: Map<String, Int>?,
    @JsonProperty("minPrice")
    val minPrice: BigDecimal?,
    @JsonProperty("maxPrice")
    val maxPrice: BigDecimal?,
)

/**
 * Response DTO for public staff information
 */
@Schema(description = "Public staff information for booking")
data class PublicStaffResponse(
    @JsonProperty("id")
    val id: String,
    @JsonProperty("name")
    val name: String,
    @JsonProperty("specialties")
    val specialties: List<String> = emptyList(),
)

/**
 * Request DTO for checking availability
 */
@Schema(description = "Request to check appointment availability")
data class CheckAvailabilityRequest(
    @field:NotBlank(message = "Staff ID is required")
    @JsonProperty("staffId")
    val staffId: String,
    @field:NotNull(message = "Appointment date is required")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @JsonProperty("appointmentDate")
    val appointmentDate: LocalDate,
    @field:NotEmpty(message = "At least one service is required")
    @JsonProperty("serviceIds")
    val serviceIds: List<String>,
)

/**
 * Response DTO for availability information
 */
@Schema(description = "Available time slots for booking")
data class AvailabilityResponse(
    @JsonProperty("date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    val date: LocalDate,
    @JsonProperty("availableSlots")
    val availableSlots: List<TimeSlot>,
)

/**
 * Time slot information
 */
@Schema(description = "Available time slot")
data class TimeSlot(
    @JsonProperty("startTime")
    @JsonFormat(pattern = "HH:mm")
    val startTime: LocalTime,
    @JsonProperty("endTime")
    @JsonFormat(pattern = "HH:mm")
    val endTime: LocalTime,
    @JsonProperty("available")
    val available: Boolean,
)

/**
 * Request DTO for creating a public booking
 */
@Schema(description = "Request to create a public booking")
data class CreatePublicBookingRequest(
    @field:NotBlank(message = "Client name is required")
    @field:Size(max = 255, message = "Client name must not exceed 255 characters")
    @JsonProperty("clientName")
    val clientName: String,
    @field:NotBlank(message = "Client phone is required")
    @field:Pattern(regexp = "^\\+?[1-9]\\d{1,14}$", message = "Invalid phone number format")
    @JsonProperty("clientPhone")
    val clientPhone: String,
    @field:Email(message = "Invalid email format")
    @JsonProperty("clientEmail")
    val clientEmail: String?,
    @field:NotBlank(message = "Pet name is required")
    @field:Size(max = 255, message = "Pet name must not exceed 255 characters")
    @JsonProperty("petName")
    val petName: String,
    @field:NotBlank(message = "Pet species is required")
    @JsonProperty("petSpecies")
    val petSpecies: String,
    @JsonProperty("petBreed")
    val petBreed: String?,
    @JsonProperty("petSize")
    val petSize: String?,
    @field:NotBlank(message = "Staff ID is required")
    @JsonProperty("staffId")
    val staffId: String,
    @field:NotNull(message = "Appointment date is required")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @JsonProperty("appointmentDate")
    val appointmentDate: LocalDate,
    @field:NotNull(message = "Start time is required")
    @JsonFormat(pattern = "HH:mm")
    @JsonProperty("startTime")
    val startTime: LocalTime,
    @field:NotNull(message = "End time is required")
    @JsonFormat(pattern = "HH:mm")
    @JsonProperty("endTime")
    val endTime: LocalTime,
    @field:NotEmpty(message = "At least one service is required")
    @JsonProperty("serviceIds")
    val serviceIds: List<String>,
    @field:Size(max = 2000, message = "Notes must not exceed 2000 characters")
    @JsonProperty("notes")
    val notes: String?,
)

/**
 * Response DTO for public booking creation
 */
@Schema(description = "Response for created public booking")
data class PublicBookingResponse(
    @JsonProperty("appointmentId")
    val appointmentId: String,
    @JsonProperty("salonName")
    val salonName: String,
    @JsonProperty("clientName")
    val clientName: String,
    @JsonProperty("petName")
    val petName: String,
    @JsonProperty("staffName")
    val staffName: String,
    @JsonProperty("appointmentDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    val appointmentDate: LocalDate,
    @JsonProperty("startTime")
    @JsonFormat(pattern = "HH:mm")
    val startTime: LocalTime,
    @JsonProperty("endTime")
    @JsonFormat(pattern = "HH:mm")
    val endTime: LocalTime,
    @JsonProperty("services")
    val services: List<PublicServiceResponse>,
    @JsonProperty("totalPrice")
    val totalPrice: BigDecimal,
    @JsonProperty("status")
    val status: String,
    @JsonProperty("notes")
    val notes: String?,
    @JsonProperty("bookingAcceptance")
    val bookingAcceptance: String,
    @JsonProperty("confirmationMessage")
    val confirmationMessage: String,
)
