package ro.animaliaprogramari.animalia.application.service

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import ro.animaliaprogramari.animalia.application.port.inbound.SalonServiceManagementUseCase
import ro.animaliaprogramari.animalia.domain.model.*

/**
 * Application service for service management operations
 * Provides business logic for salon service-related operations
 */
@Service
class ServiceManagementService(
    private val salonServiceManagementUseCase: SalonServiceManagementUseCase,
) {
    private val logger = LoggerFactory.getLogger(ServiceManagementService::class.java)

    /**
     * Get all active services for a salon
     */
    fun getActiveServices(salonId: SalonId): List<SalonService> {
        logger.info("Getting active services for salon: ${salonId.value}")
        return salonServiceManagementUseCase.getServicesBySalon(
            salonId = salonId,
            activeOnly = true
        )
    }

    /**
     * Get all services for a salon (active and inactive)
     */
    fun getAllServices(salonId: SalonId): List<SalonService> {
        logger.info("Getting all services for salon: ${salonId.value}")
        return salonServiceManagementUseCase.getServicesBySalon(
            salonId = salonId,
            activeOnly = false
        )
    }

    /**
     * Get service by ID
     */
    fun getServiceById(serviceId: ServiceId, salonId: SalonId): SalonService? {
        logger.info("Getting service by ID: ${serviceId.value} for salon: ${salonId.value}")
        return salonServiceManagementUseCase.getServiceById(serviceId, salonId)
    }

    /**
     * Get services by IDs
     */
    fun getServicesByIds(salonId: SalonId, serviceIds: List<ServiceId>): List<SalonService> {
        logger.info("Getting services by IDs for salon: ${salonId.value}, service count: ${serviceIds.size}")
        return serviceIds.mapNotNull { serviceId ->
            salonServiceManagementUseCase.getServiceById(serviceId, salonId)
        }
    }

    /**
     * Get services by category
     */
    fun getServicesByCategory(salonId: SalonId, category: ServiceCategory): List<SalonService> {
        logger.info("Getting services by category for salon: ${salonId.value}, category: ${category.name}")
        return salonServiceManagementUseCase.getServicesBySalon(
            salonId = salonId,
            activeOnly = true,
            category = category
        )
    }

    /**
     * Search services
     */
    fun searchServices(salonId: SalonId, search: String): List<SalonService> {
        logger.info("Searching services for salon: ${salonId.value}, query: $search")
        return salonServiceManagementUseCase.getServicesBySalon(
            salonId = salonId,
            activeOnly = true,
            search = search
        )
    }

    /**
     * Create a new service
     */
    fun createService(
        salonId: SalonId,
        name: String,
        description: String?,
        price: Money,
        myDuration: MyDuration,
        category: ServiceCategory,
        displayOrder: Int = 0,
        requirements: List<String> = emptyList(),
        sizePrices: Map<String, Money> = emptyMap(),
        sizeDurations: Map<String, MyDuration> = emptyMap(),
        minPrice: Money? = null,
        maxPrice: Money? = null,
        sizeMinPrices: Map<String, Money>? = null,
        sizeMaxPrices: Map<String, Money>? = null,
        color: String? = null,
    ): SalonService {
        logger.info("Creating service for salon: ${salonId.value}")
        return salonServiceManagementUseCase.createService(
            salonId, name, description, price, myDuration, category,
            displayOrder, requirements, sizePrices, sizeDurations,
            minPrice, maxPrice, sizeMinPrices, sizeMaxPrices, color
        )
    }

    /**
     * Update an existing service
     */
    fun updateService(
        serviceId: ServiceId,
        salonId: SalonId,
        name: String? = null,
        description: String? = null,
        price: Money? = null,
        myDuration: MyDuration? = null,
        category: ServiceCategory? = null,
        displayOrder: Int? = null,
        requirements: List<String>? = null,
        sizePrices: Map<String, Money>? = null,
        sizeDurations: Map<String, MyDuration>? = null,
        minPrice: Money? = null,
        maxPrice: Money? = null,
        sizeMinPrices: Map<String, Money>? = null,
        sizeMaxPrices: Map<String, Money>? = null,
        color: String? = null,
    ): SalonService {
        logger.info("Updating service: ${serviceId.value}")
        return salonServiceManagementUseCase.updateService(
            serviceId, salonId, name, description, price, myDuration, category,
            displayOrder, requirements, sizePrices, sizeDurations,
            minPrice, maxPrice, sizeMinPrices, sizeMaxPrices, color
        )
    }

    /**
     * Deactivate a service
     */
    fun deactivateService(serviceId: ServiceId, salonId: SalonId): SalonService {
        logger.info("Deactivating service: ${serviceId.value}")
        return salonServiceManagementUseCase.deactivateService(serviceId, salonId)
    }

    /**
     * Activate a service
     */
    fun activateService(serviceId: ServiceId, salonId: SalonId): SalonService {
        logger.info("Activating service: ${serviceId.value}")
        return salonServiceManagementUseCase.activateService(serviceId, salonId)
    }

    /**
     * Delete a service permanently
     */
    fun deleteService(serviceId: ServiceId, salonId: SalonId) {
        logger.info("Deleting service: ${serviceId.value}")
        salonServiceManagementUseCase.deleteService(serviceId, salonId)
    }

    /**
     * Check if service name is unique
     */
    fun isServiceNameUnique(salonId: SalonId, name: String, excludeServiceId: ServiceId? = null): Boolean {
        logger.info("Checking service name uniqueness for salon: ${salonId.value}, name: $name")
        return salonServiceManagementUseCase.isServiceNameUnique(salonId, name, excludeServiceId)
    }
}
