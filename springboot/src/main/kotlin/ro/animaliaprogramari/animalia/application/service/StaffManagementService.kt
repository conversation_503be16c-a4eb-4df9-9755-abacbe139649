package ro.animaliaprogramari.animalia.application.service

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import ro.animaliaprogramari.animalia.application.port.outbound.StaffRepository
import ro.animaliaprogramari.animalia.application.port.outbound.StaffWithAppointmentCount
import ro.animaliaprogramari.animalia.application.port.outbound.StaffPerformanceUpdate
import ro.animaliaprogramari.animalia.domain.model.*
import java.time.LocalDate

/**
 * Application service for staff management operations
 * Provides business logic for staff-related operations
 */
@Service
class StaffManagementService(
    private val staffRepository: StaffRepository,
) {
    private val logger = LoggerFactory.getLogger(StaffManagementService::class.java)

    /**
     * Get all active staff members for a salon
     */
    fun getActiveStaff(salonId: SalonId): List<Staff> {
        logger.info("Getting active staff for salon: ${salonId.value}")
        return staffRepository.findActiveBySalonWithUserDetails(salonId)
    }

    /**
     * Get all staff members for a salon (active and inactive)
     */
    fun getAllStaff(salonId: SalonId): List<Staff> {
        logger.info("Getting all staff for salon: ${salonId.value}")
        return staffRepository.findBySalonWithUserDetails(salonId)
    }

    /**
     * Get staff member by ID
     */
    fun getStaffById(staffId: StaffId): Staff? {
        logger.info("Getting staff by ID: ${staffId.value}")
        return staffRepository.findById(staffId)
    }

    /**
     * Get staff member by user ID and salon ID
     */
    fun getStaffByUserIdAndSalonId(userId: UserId, salonId: SalonId): Staff? {
        logger.info("Getting staff by user ID: ${userId.value} and salon ID: ${salonId.value}")
        return staffRepository.findByUserIdAndSalonId(userId, salonId)
    }

    /**
     * Get staff members with specific specializations
     */
    fun getStaffBySpecializations(salonId: SalonId, specializations: Set<Specialization>): List<Staff> {
        logger.info("Getting staff with specializations for salon: ${salonId.value}")
        return staffRepository.findBySpecializations(salonId, specializations)
    }

    /**
     * Get staff members who can handle a specific service category
     */
    fun getStaffByServiceCapability(salonId: SalonId, serviceCategory: ServiceCategory): List<Staff> {
        logger.info("Getting staff with service capability for salon: ${salonId.value}, category: ${serviceCategory.name}")
        return staffRepository.findByServiceCapability(salonId, serviceCategory)
    }

    /**
     * Get available staff for a specific time slot
     */
    fun getAvailableStaff(
        salonId: SalonId,
        date: LocalDate,
        timeSlot: TimeSlot,
        excludeStaffIds: List<StaffId> = emptyList()
    ): List<Staff> {
        logger.info("Getting available staff for salon: ${salonId.value}, date: $date, time slot: $timeSlot")
        return staffRepository.findAvailableStaff(salonId, date, timeSlot, excludeStaffIds)
    }

    /**
     * Get high performing staff members
     */
    fun getHighPerformingStaff(
        salonId: SalonId,
        minimumRating: Double,
        minimumAppointments: Int
    ): List<Staff> {
        logger.info("Getting high performing staff for salon: ${salonId.value}")
        return staffRepository.findHighPerformingStaff(salonId, minimumRating, minimumAppointments)
    }

    /**
     * Count active staff members in a salon
     */
    fun countActiveStaff(salonId: SalonId): Long {
        logger.info("Counting active staff for salon: ${salonId.value}")
        return staffRepository.countActiveBySalon(salonId)
    }

    /**
     * Get staff members hired within a date range
     */
    fun getStaffHiredBetween(
        salonId: SalonId,
        startDate: LocalDate,
        endDate: LocalDate
    ): List<Staff> {
        logger.info("Getting staff hired between $startDate and $endDate for salon: ${salonId.value}")
        return staffRepository.findByHireDateBetween(salonId, startDate, endDate)
    }

    /**
     * Get staff workload metrics for a period
     */
    fun getStaffWorkloadMetrics(
        staffIds: List<StaffId>,
        startDate: LocalDate,
        endDate: LocalDate
    ): Map<StaffId, WorkloadMetrics> {
        logger.info("Getting workload metrics for ${staffIds.size} staff members")
        return staffRepository.getWorkloadMetrics(staffIds, startDate, endDate)
    }

    /**
     * Get staff members with their appointment counts
     */
    fun getStaffWithAppointmentCounts(
        salonId: SalonId,
        startDate: LocalDate,
        endDate: LocalDate
    ): List<StaffWithAppointmentCount> {
        logger.info("Getting staff with appointment counts for salon: ${salonId.value}")
        return staffRepository.findStaffWithAppointmentCounts(salonId, startDate, endDate)
    }

    /**
     * Save staff member
     */
    fun saveStaff(staff: Staff): Staff {
        logger.info("Saving staff member: ${staff.id.value}")
        return staffRepository.save(staff)
    }

    /**
     * Delete staff member
     */
    fun deleteStaff(staffId: StaffId) {
        logger.info("Deleting staff member: ${staffId.value}")
        staffRepository.delete(staffId)
    }

    /**
     * Update staff performance metrics in batch
     */
    fun updatePerformanceMetrics(updates: List<StaffPerformanceUpdate>) {
        logger.info("Updating performance metrics for ${updates.size} staff members")
        staffRepository.updatePerformanceMetrics(updates)
    }
}
